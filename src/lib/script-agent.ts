/**
 * Simple Script Generation Agent using LangGraph
 *
 * This agent demonstrates how to build autonomous AI systems using LangGraph.
 * LangGraph is a framework for building stateful, multi-step AI workflows where
 * agents can make decisions, use tools, and maintain state across multiple steps.
 *
 * Key LangGraph Concepts:
 * 1. **State**: Shared data that flows through all nodes in the graph
 * 2. **Nodes**: Individual functions that process the state and return updates
 * 3. **Edges**: Connections between nodes that define the workflow flow
 * 4. **Graph**: The overall workflow that orchestrates nodes and edges
 * 5. **Compilation**: Converting the graph definition into an executable workflow
 *
 * This agent takes a user's video idea and generates a structured script.
 */

import { ChatOpenAI } from '@langchain/openai'
import { StateGraph, START, END } from '@langchain/langgraph'
import { Annotation } from '@langchain/langgraph'
import { HumanMessage, AIMessage } from '@langchain/core/messages'
import {
  extractBlogMarkdownAndImages,
  extractPDFMarkdownAndImages,
} from '@/inngest/utils/content-extractor'

/**
 * STATE DEFINITION - The Heart of LangGraph
 *
 * In LangGraph, the "state" is a shared data structure that flows through
 * all nodes in the workflow. Think of it as a "context" that gets passed
 * from one step to the next, allowing each node to:
 * 1. Read data from previous steps
 * 2. Add new data for future steps
 * 3. Update existing data
 *
 * Annotation.Root() creates a type-safe state schema where:
 * - Each field has a specific type
 * - Optional reducers define how updates are merged
 * - Default values can be provided
 *
 * State flows like this: Initial State → Node 1 → Updated State → Node 2 → Final State
 */
export const ScriptState = Annotation.Root({
  // INPUT FIELDS - Data provided by the user/caller
  userIdea: Annotation<string>, // The video idea from the user (could be text idea or blog URL)
  duration: Annotation<number>, // Target video duration in seconds

  // CONTENT PARAMETERS - How the script should be crafted
  tone: Annotation<string>, // Writing tone: 'friendly', 'professional', 'energetic', etc.
  audience: Annotation<string>, // Target audience: 'coffee enthusiasts', 'remote workers', etc.
  platform: Annotation<string>, // Target platform: 'YouTube', 'TikTok', 'LinkedIn', etc.
  language: Annotation<string>, // Script language: 'English', 'Spanish', etc.
  keywords: Annotation<string | null>, // Keywords to incorporate naturally
  hook: Annotation<boolean>, // Whether to include attention-grabbing opening
  callToAction: Annotation<boolean>, // Whether to include platform-specific CTA

  // CONTENT PARSING FIELDS - For different input types
  inputType: Annotation<'idea' | 'blog_url' | 'pdf_url' | 'text'>, // Detected input type
  blogContent: Annotation<string | null>, // Extracted blog content (markdown)
  blogSummary: Annotation<string | null>, // LLM-generated summary of blog content
  pdfContent: Annotation<string | null>, // Extracted PDF content (markdown)
  pdfSummary: Annotation<string | null>, // LLM-generated summary of PDF content
  textContent: Annotation<string | null>, // Raw text content
  textSummary: Annotation<string | null>, // LLM-generated summary of text content
  extractedKeywords: Annotation<string[] | null>, // Keywords extracted from content

  // PROCESSING FIELDS - Data used during agent execution
  messages: Annotation<(HumanMessage | AIMessage)[]>({
    // Reducer function: defines how new messages are merged with existing ones
    // In this case, we concatenate new messages to the existing array
    reducer: (x, y) => x.concat(y),
    default: () => [], // Start with empty message array
  }),

  // OUTPUT FIELDS - Data generated by the agent
  script: Annotation<{
    title: string
    scenes: Array<{
      id: string
      sceneNumber: number
      text: string
      duration: number
      searchKeywords: string[]
    }>
  } | null>, // null initially, populated by the script generation node

  // STATUS TRACKING - Monitor the agent's progress
  status: Annotation<'processing' | 'completed' | 'failed'>({
    // Reducer: always use the latest status (replace, don't merge)
    reducer: (x, y) => y,
    default: () => 'processing', // Start in processing state
  }),
  error: Annotation<string | null>, // Error message if something goes wrong
})

// Type helper: Extract the TypeScript type from our state annotation
export type ScriptStateType = typeof ScriptState.State

/**
 * UTILITY FUNCTIONS - Helper functions for the agent
 */

/**
 * Detects the type of input provided by the user
 * @param input - The user's input string
 * @returns 'blog_url', 'pdf_url', 'text', or 'idea' based on content analysis
 */
function detectInputType(
  input: string
): 'idea' | 'blog_url' | 'pdf_url' | 'text' {
  const trimmedInput = input.trim()

  // Check for PDF URLs
  if (
    trimmedInput.match(/^https?:\/\/.*\.pdf$/i) ||
    (trimmedInput.includes('.pdf') && trimmedInput.match(/^https?:\/\//)) ||
    (trimmedInput.includes('format=pdf') && trimmedInput.match(/^https?:\/\//))
  ) {
    return 'pdf_url'
  }

  // Check for general URLs (blog URLs)
  const urlPattern = /^https?:\/\/|www\.|\.com|\.org|\.net|\.edu|\.gov/i
  if (urlPattern.test(trimmedInput)) {
    return 'blog_url'
  }

  // Check for long text content (likely pasted text)
  // If input is longer than 500 characters and contains multiple sentences, treat as text
  if (
    trimmedInput.length > 500 &&
    (trimmedInput.match(/\./g) || []).length > 3
  ) {
    return 'text'
  }

  // Default to idea for short text inputs
  return 'idea'
}

/**
 * NODE FUNCTIONS - The Building Blocks of Our Workflow
 *
 * NODE FUNCTION - The Core Processing Unit
 *
 * In LangGraph, a "node" is a function that:
 * 1. Receives the current state as input
 * 2. Performs some processing (LLM calls, API calls, computations, etc.)
 * 3. Returns a partial state update (only the fields that changed)
 *
 * Node Function Signature:
 * - Input: Current state (ScriptStateType)
 * - Output: Partial state update (Partial<ScriptStateType>)
 *
 * LangGraph automatically merges the returned partial state with the existing state,
 * so you only need to return the fields you want to update.
 *
 * This node handles the core script generation logic using OpenAI's LLM.
 */

/**
 * Input Detection Node - Determines if input is a blog URL or text idea
 * This node analyzes the user input and sets the processing path accordingly.
 */
async function inputDetectionNode(
  state: ScriptStateType
): Promise<Partial<ScriptStateType>> {
  console.log('🔍 Input Detection Node - Starting')
  console.log('📝 Analyzing input:', state.userIdea)

  const inputType = detectInputType(state.userIdea)
  console.log('🎯 Detected input type:', inputType)

  return {
    inputType,
    status: 'processing' as const,
  }
}

/**
 * Blog Parsing Node - Extracts and analyzes blog content using LLM
 * This node fetches blog content, extracts key information, and prepares it for script generation.
 */
async function blogParsingNode(
  state: ScriptStateType
): Promise<Partial<ScriptStateType>> {
  console.log('📰 Blog Parsing Node - Starting')
  console.log('🔗 Blog URL:', state.userIdea)

  try {
    // Step 1: Extract blog content using existing utility
    console.log('📥 Extracting blog content...')
    const { markdown } = await extractBlogMarkdownAndImages(state.userIdea)

    // Step 2: Use LLM to analyze and summarize blog content
    console.log('🤖 Analyzing blog content with LLM...')
    const llm = new ChatOpenAI({
      model: 'gpt-4o-mini',
      temperature: 0.3,
      openAIApiKey: process.env.OPENAI_API_KEY,
    })

    const analysisPrompt = `You are an expert content analyst. Analyze the following blog article and extract key information for video script creation.

Blog Content (Markdown):
${markdown}

Please provide a JSON response with the following structure:
{
  "summary": "A concise 2-3 sentence summary of the main topic and key message",
  "keyPoints": ["Point 1", "Point 2", "Point 3", "Point 4", "Point 5"],
  "extractedKeywords": ["keyword1", "keyword2", "keyword3", "keyword4", "keyword5"],
  "suggestedTitle": "A compelling video title based on the blog content",
  "mainTheme": "The central theme or topic of the blog",
  "targetAudience": "Who this content would appeal to most",
  "mentionedCompanyOrProduct": {
    "name": "Company or product name if mentioned, otherwise null",
    "role": "How the company/product is positioned (e.g., 'solution provider', 'recommended tool')",
    "callToAction": "A clear call-to-action message if present (e.g., 'contact us', 'learn more', 'book a demo')"
  }
}

Focus on:
1. The most important and engaging points that would work well in a video
2. Keywords that are naturally mentioned and relevant
3. The core message that viewers should understand
4. Visual concepts that could be illustrated
5. Detecting if the blog highlights a company, product, or service as a solution — and include its name, role, and any suggested action for the audience
6. If no company or product is mentioned, return null for that section

IMPORTANT: Return ONLY the raw JSON object without any markdown formatting, code fences, or additional text. Do not wrap the response in \`\`\`json or \`\`\` markers.`

    const response = await llm.invoke([
      { role: 'user', content: analysisPrompt },
    ])

    console.log('✅ Received LLM analysis')
    console.log('📄 Raw Response:', response.content)

    // Parse the JSON response with robust handling of markdown code fences
    let analysisResult
    try {
      // Extract JSON from response, handling markdown code fences
      let jsonString = response.content as string
      jsonString = jsonString.trim()

      // Remove markdown code fences if present
      if (jsonString.startsWith('```')) {
        console.log('🔧 Removing markdown code fences from response')
        jsonString = jsonString
          .replace(/^```(?:json)?\n?/, '')
          .replace(/\n?```$/, '')
          .trim()
        console.log(
          '🧹 Cleaned JSON string:',
          jsonString.substring(0, 200) + '...'
        )
      }

      analysisResult = JSON.parse(jsonString)
      console.log('✅ Successfully parsed analysis JSON')
      console.log('🎯 Analysis Result:', analysisResult)
    } catch (parseError) {
      console.error('❌ Failed to parse LLM analysis JSON:', parseError)
      console.error('📄 Raw content that failed to parse:', response.content)
      throw new Error('Failed to parse blog analysis from LLM')
    }

    return {
      blogContent: markdown,
      blogSummary: analysisResult.summary,
      extractedKeywords: analysisResult.extractedKeywords,
      // Update userIdea to use the analyzed content for script generation
      userIdea: `${analysisResult.suggestedTitle}\n\nKey Points:\n${analysisResult.keyPoints.map((point: string, i: number) => `${i + 1}. ${point}`).join('\n')}\n\nMain Theme: ${analysisResult.mainTheme}\n\nTarget Audience: ${analysisResult.targetAudience}\n\n${analysisResult.mentionedCompanyOrProduct.name && `Company/Product Mentioned: ${analysisResult.mentionedCompanyOrProduct.name} - ${analysisResult.mentionedCompanyOrProduct.role} - ${analysisResult.mentionedCompanyOrProduct.callToAction}`}`,
      status: 'processing' as const,
    }
  } catch (error) {
    console.error('❌ Blog parsing failed:', error)
    return {
      status: 'failed' as const,
      error: error instanceof Error ? error.message : 'Blog parsing failed',
    }
  }
}

/**
 * PDF Parsing Node - Extracts and analyzes PDF content using LLM
 * This node fetches PDF content, extracts key information, and prepares it for script generation.
 */
async function pdfParsingNode(
  state: ScriptStateType
): Promise<Partial<ScriptStateType>> {
  console.log('📄 PDF Parsing Node - Starting')
  console.log('🔗 PDF URL:', state.userIdea)

  try {
    // Step 1: Extract PDF content using existing utility
    console.log('📥 Extracting PDF content...')
    const { markdown, pdf_images } = await extractPDFMarkdownAndImages(
      state.userIdea
    )
    console.log('📄 Content length:', markdown.length, 'characters')
    console.log('🖼️ PDF images found:', pdf_images.length)

    // Step 2: Use LLM to analyze and summarize PDF content
    console.log('🤖 Analyzing PDF content with LLM...')
    const llm = new ChatOpenAI({
      model: 'gpt-4o-mini',
      temperature: 0.3,
      openAIApiKey: process.env.OPENAI_API_KEY,
    })

    const analysisPrompt = `Analyze this PDF document content and extract key information for video script creation:

${markdown}

Extract and return the following information in JSON format:
{
  "summary": "Brief summary of the main topic and key points",
  "keyPoints": ["Point 1", "Point 2", "Point 3", "Point 4", "Point 5"],
  "extractedKeywords": ["keyword1", "keyword2", "keyword3", "keyword4", "keyword5"],
  "suggestedTitle": "Compelling video title based on the content",
  "mainTheme": "The central theme or topic",
  "targetAudience": "Who this content would appeal to most",
  "mentionedCompanyOrProduct": {
    "name": "Company or product name if mentioned, otherwise null",
    "role": "How the company/product is positioned (e.g., 'solution provider', 'recommended tool')",
    "callToAction": "A clear call-to-action message if present (e.g., 'contact us', 'learn more', 'book a demo')"
  }
}

Focus on:
1. The most important and engaging points that would work well in a video
2. Keywords that are naturally mentioned and relevant
3. The core message that viewers should understand
4. Visual concepts that could be illustrated
5. Detecting if the PDF highlights a company, product, or service as a solution — and include its name, role, and any suggested action for the audience
6. If no company or product is mentioned, return null for that section

IMPORTANT: Return ONLY the raw JSON object without any markdown formatting, code fences, or additional text. Do not wrap the response in \`\`\`json or \`\`\` markers.`

    const response = await llm.invoke([
      { role: 'user', content: analysisPrompt },
    ])

    console.log('✅ Received LLM analysis')
    console.log('📄 Raw Response:', response.content)

    // Parse the JSON response with robust handling of markdown code fences
    let analysisResult
    try {
      // Extract JSON from response, handling markdown code fences
      let jsonString = response.content as string
      jsonString = jsonString.trim()

      // Remove markdown code fences if present
      if (jsonString.startsWith('```')) {
        console.log('🔧 Removing markdown code fences from response')
        jsonString = jsonString
          .replace(/^```(?:json)?\n?/, '')
          .replace(/\n?```$/, '')
          .trim()
        console.log(
          '🧹 Cleaned JSON string:',
          jsonString.substring(0, 200) + '...'
        )
      }

      analysisResult = JSON.parse(jsonString)
      console.log('✅ Successfully parsed analysis JSON')
      console.log('🎯 Analysis Result:', analysisResult)
    } catch (parseError) {
      console.error('❌ Failed to parse LLM analysis JSON:', parseError)
      console.error('📄 Raw content that failed to parse:', response.content)
      throw new Error('Failed to parse PDF analysis from LLM')
    }

    return {
      pdfContent: markdown,
      pdfSummary: analysisResult.summary,
      extractedKeywords: analysisResult.extractedKeywords,
      // Transform the PDF analysis into a video-ready format
      userIdea: `${analysisResult.suggestedTitle}

Key Points:
${analysisResult.keyPoints.map((point: string, index: number) => `${index + 1}. ${point}`).join('\n')}

Main Theme: ${analysisResult.mainTheme}
Target Audience: ${analysisResult.targetAudience}

${
  analysisResult.mentionedCompanyOrProduct?.name
    ? `Featured Solution: ${analysisResult.mentionedCompanyOrProduct.name} (${analysisResult.mentionedCompanyOrProduct.role})
  ${analysisResult.mentionedCompanyOrProduct.callToAction ? `Call-to-Action: ${analysisResult.mentionedCompanyOrProduct.callToAction}` : ''}`
    : ''
}`,
      status: 'processing' as const,
    }
  } catch (error) {
    console.error('❌ PDF parsing failed:', error)
    return {
      status: 'failed' as const,
      error: `PDF parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
    }
  }
}

/**
 * Text Parsing Node - Analyzes plain text content using LLM
 * This node processes raw text input and prepares it for script generation.
 */
async function textParsingNode(
  state: ScriptStateType
): Promise<Partial<ScriptStateType>> {
  console.log('📝 Text Parsing Node - Starting')
  console.log('📄 Text length:', state.userIdea.length, 'characters')

  try {
    // Step 1: Store the raw text content
    const textContent = state.userIdea

    // Step 2: Use LLM to analyze and structure the text content
    console.log('🤖 Analyzing text content with LLM...')
    const llm = new ChatOpenAI({
      model: 'gpt-4o-mini',
      temperature: 0.3,
      openAIApiKey: process.env.OPENAI_API_KEY,
    })

    const analysisPrompt = `Analyze this text content and extract key information for video script creation:

${textContent}

Extract and return the following information in JSON format:
{
  "summary": "Brief summary of the main topic and key points",
  "keyPoints": ["Point 1", "Point 2", "Point 3", "Point 4", "Point 5"],
  "extractedKeywords": ["keyword1", "keyword2", "keyword3", "keyword4", "keyword5"],
  "suggestedTitle": "Compelling video title based on the content",
  "mainTheme": "The central theme or topic",
  "targetAudience": "Who this content would appeal to most",
  "mentionedCompanyOrProduct": {
    "name": "Company or product name if mentioned, otherwise null",
    "role": "How the company/product is positioned (e.g., 'solution provider', 'recommended tool')",
    "callToAction": "A clear call-to-action message if present (e.g., 'contact us', 'learn more', 'book a demo')"
  }
}

Focus on:
1. The most important and engaging points that would work well in a video
2. Keywords that are naturally mentioned and relevant
3. The core message that viewers should understand
4. Visual concepts that could be illustrated
5. Detecting if the text highlights a company, product, or service as a solution — and include its name, role, and any suggested action for the audience
6. If no company or product is mentioned, return null for that section

IMPORTANT: Return ONLY the raw JSON object without any markdown formatting, code fences, or additional text. Do not wrap the response in \`\`\`json or \`\`\` markers.`

    const response = await llm.invoke([
      { role: 'user', content: analysisPrompt },
    ])

    console.log('✅ Received LLM analysis')
    console.log('📄 Raw Response:', response.content)

    // Parse the JSON response with robust handling of markdown code fences
    let analysisResult
    try {
      // Extract JSON from response, handling markdown code fences
      let jsonString = response.content as string
      jsonString = jsonString.trim()

      // Remove markdown code fences if present
      if (jsonString.startsWith('```')) {
        console.log('🔧 Removing markdown code fences from response')
        jsonString = jsonString
          .replace(/^```(?:json)?\n?/, '')
          .replace(/\n?```$/, '')
          .trim()
        console.log(
          '🧹 Cleaned JSON string:',
          jsonString.substring(0, 200) + '...'
        )
      }

      analysisResult = JSON.parse(jsonString)
      console.log('✅ Successfully parsed analysis JSON')
      console.log('🎯 Analysis Result:', analysisResult)
    } catch (parseError) {
      console.error('❌ Failed to parse LLM analysis JSON:', parseError)
      console.error('📄 Raw content that failed to parse:', response.content)
      throw new Error('Failed to parse text analysis from LLM')
    }

    return {
      textContent: textContent,
      textSummary: analysisResult.summary,
      extractedKeywords: analysisResult.extractedKeywords,
      // Transform the text analysis into a video-ready format
      userIdea: `${analysisResult.suggestedTitle}

Key Points:
${analysisResult.keyPoints.map((point: string, index: number) => `${index + 1}. ${point}`).join('\n')}

Main Theme: ${analysisResult.mainTheme}
Target Audience: ${analysisResult.targetAudience}

${
  analysisResult.mentionedCompanyOrProduct?.name
    ? `Featured Solution: ${analysisResult.mentionedCompanyOrProduct.name} (${analysisResult.mentionedCompanyOrProduct.role})
  ${analysisResult.mentionedCompanyOrProduct.callToAction ? `Call-to-Action: ${analysisResult.mentionedCompanyOrProduct.callToAction}` : ''}`
    : ''
}`,
      status: 'processing' as const,
    }
  } catch (error) {
    console.error('❌ Text parsing failed:', error)
    return {
      status: 'failed' as const,
      error: `Text parsing failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
    }
  }
}

async function generateScriptNode(
  state: ScriptStateType
): Promise<Partial<ScriptStateType>> {
  console.log('🎬 Script Generation Node - Starting')
  console.log('📝 User Idea:', state.userIdea)
  console.log('⏱️ Target Duration:', state.duration, 'seconds')
  console.log('🎭 Tone:', state.tone)
  console.log('👥 Audience:', state.audience)
  console.log('📱 Platform:', state.platform)
  console.log('🌍 Language:', state.language)
  console.log('🔑 Keywords:', state.keywords || 'None')
  console.log('🪝 Hook Required:', state.hook ? 'Yes' : 'No')
  console.log('📢 Call-to-Action:', state.callToAction ? 'Yes' : 'No')

  try {
    // Initialize OpenAI LLM
    const llm = new ChatOpenAI({
      modelName: 'gpt-4o-mini', // Using the cheaper model for testing
      temperature: 0.7,
      openAIApiKey: process.env.OPENAI_API_KEY,
    })

    // Create the sophisticated prompt for script generation
    // Character-based scene division: ~25-30 characters per second of speech
    const charactersPerSecond = 27 // Average speaking pace
    const maxCharactersPerScene = 200 // Maximum characters per scene for readability
    const totalCharactersNeeded = state.duration * charactersPerSecond
    const estimatedScenesCount = Math.ceil(
      totalCharactersNeeded / maxCharactersPerScene
    )

    console.log(`📊 Scene Division Calculation:`)
    console.log(`   Duration: ${state.duration}s`)
    console.log(`   Characters per second: ${charactersPerSecond}`)
    console.log(`   Total characters needed: ${totalCharactersNeeded}`)
    console.log(`   Max characters per scene: ${maxCharactersPerScene}`)
    console.log(`   Estimated scenes: ${estimatedScenesCount}`)

    // Build platform-specific guidance
    const platformGuidance = {
      YouTube:
        'engaging storytelling with clear structure, educational or entertaining content',
      TikTok:
        'fast-paced, trendy, hook within first 3 seconds, visual-first content',
      Instagram:
        'visually appealing, lifestyle-focused, authentic and relatable',
      LinkedIn: 'professional insights, business value, thought leadership',
      Facebook: 'community-focused, shareable, conversational tone',
      Twitter: 'concise, punchy, news-worthy or opinion-driven',
    }

    // Build tone guidance
    const toneGuidance = {
      friendly: 'warm, approachable, conversational, like talking to a friend',
      professional: 'authoritative, credible, polished, business-appropriate',
      energetic: 'enthusiastic, dynamic, exciting, high-energy language',
      casual: 'relaxed, informal, everyday language, laid-back',
      educational: 'informative, clear explanations, teaching-focused',
      inspirational: 'motivating, uplifting, empowering, positive',
      humorous: 'witty, entertaining, light-hearted, fun',
    }

    const prompt = `You are an expert video script writer specializing in ${state.platform} content. Create a compelling, platform-optimized video script.

**CONTENT BRIEF:**
Video Idea: "${state.userIdea}"
Target Duration: ${state.duration} seconds
Tone: ${state.tone} (${toneGuidance[state.tone as keyof typeof toneGuidance] || 'maintain this tone throughout'})
Target Audience: ${state.audience}
Platform: ${state.platform} (${platformGuidance[state.platform as keyof typeof platformGuidance] || 'optimize for this platform'})
Language: ${state.language}
${state.keywords ? `Keywords to incorporate: ${state.keywords}` : ''}
${state.hook ? 'MUST include attention-grabbing hook in first scene' : ''}
${state.callToAction ? `MUST include ${state.platform}-appropriate call-to-action in final scene` : ''}

**SCRIPT REQUIREMENTS:**
1. Create scenes based on content length (estimated ${estimatedScenesCount} scenes, but adjust as needed)
2. Each scene should have a MAXIMUM of ${maxCharactersPerScene} characters of text
3. Scene duration should be proportional to character count (~${charactersPerSecond} characters per second)
4. No artificial limit on number of scenes - create as many as needed for natural content flow
5. Write in ${state.tone} tone, targeting ${state.audience}
6. Optimize content style for ${state.platform} audience expectations
7. ${state.hook ? 'Scene 1 MUST start with a compelling hook that grabs attention immediately' : 'Start with engaging opening'}
8. ${state.callToAction ? `Final scene MUST end with ${state.platform}-specific call-to-action (e.g., ${state.platform === 'YouTube' ? '"Subscribe and hit the bell!"' : state.platform === 'TikTok' ? '"Follow for more tips!"' : state.platform === 'LinkedIn' ? '"Connect with me for more insights!"' : '"Follow us for more!"'})` : 'End with natural conclusion'}
9. ${state.keywords ? `Naturally incorporate these keywords: ${state.keywords}` : 'Use relevant keywords for the topic'}
10. Each scene needs visual search keywords for media selection
11. Total duration must equal ${state.duration} seconds (±2 seconds acceptable)
12. Write entirely in ${state.language}

**PLATFORM-SPECIFIC OPTIMIZATION:**
${state.platform === 'TikTok' ? '- Hook within first 3 seconds\n- Fast-paced, trendy language\n- Visual-first approach' : ''}
${state.platform === 'YouTube' ? '- Clear structure with intro/body/conclusion\n- Educational or entertaining value\n- Longer-form storytelling' : ''}
${state.platform === 'LinkedIn' ? '- Professional insights and value\n- Business-focused language\n- Thought leadership angle' : ''}
${state.platform === 'Instagram' ? '- Visually appealing concepts\n- Lifestyle and aesthetic focus\n- Authentic, relatable tone' : ''}

**SCENE DURATION CALCULATION:**
- Calculate each scene's duration based on character count: Math.ceil(text.length / ${charactersPerSecond})
- Minimum scene duration: 4 seconds
- Maximum scene duration: 12 seconds
- Ensure total duration equals ${state.duration} seconds
- IMPORTANT: Duration must be a NUMBER, not a string (e.g., 8, not "8s")

Return your response in this exact JSON format:
{
  "title": "Compelling ${state.platform}-optimized title in ${state.language}",
  "scenes": [
    {
      "id": "scene_1",
      "sceneNumber": 1,
      "text": "Scene narration text in ${state.tone} tone, ${state.language} language (max ${maxCharactersPerScene} characters)",
      "duration": 8,
      "searchKeywords": ["visual_keyword1", "visual_keyword2", "visual_keyword3"]
    }
  ]
}

CRITICAL: Ensure total scene durations = ${state.duration} seconds. Make the script ${state.tone} and perfect for ${state.audience} on ${state.platform}.`

    console.log('🤖 Sending prompt to OpenAI...', prompt)

    // Send to LLM
    const humanMessage = new HumanMessage(prompt)
    const response = await llm.invoke([humanMessage])

    console.log('✅ Received response from OpenAI')
    console.log('📄 Raw Response:', response.content)

    // Parse the JSON response
    let parsedScript
    try {
      // Extract JSON from the response (in case there's extra text)
      const jsonMatch = response.content.toString().match(/\{[\s\S]*\}/)
      if (!jsonMatch) {
        throw new Error('No JSON found in response')
      }

      parsedScript = JSON.parse(jsonMatch[0])
      console.log('✅ Successfully parsed script JSON')
      console.log('🎯 Generated Script:', JSON.stringify(parsedScript, null, 2))
    } catch (parseError) {
      console.error('❌ Failed to parse script JSON:', parseError)
      throw new Error(`Failed to parse script: ${parseError}`)
    }

    // Validate the script structure
    if (
      !parsedScript.title ||
      !parsedScript.scenes ||
      !Array.isArray(parsedScript.scenes)
    ) {
      throw new Error('Invalid script structure - missing title or scenes')
    }

    console.log('🎉 Script generation completed successfully!')
    console.log(`📊 Generated ${parsedScript.scenes.length} scenes`)

    return {
      messages: [humanMessage, new AIMessage(response.content.toString())],
      script: parsedScript,
      status: 'completed',
    }
  } catch (error) {
    console.error('❌ Script generation failed:', error)
    return {
      status: 'failed',
      error: error instanceof Error ? error.message : 'Unknown error occurred',
    }
  }
}

/**
 * WORKFLOW CREATION - Building the LangGraph
 *
 * This function creates and compiles the LangGraph workflow. Here's how it works:
 *
 * 1. **StateGraph**: Creates a new graph with our state schema
 * 2. **addNode**: Adds processing nodes (functions) to the graph
 * 3. **addEdge**: Defines the flow between nodes
 * 4. **compile**: Converts the graph definition into an executable workflow
 *
 * Graph Structure:
 * START → generate_script → END
 *
 * Flow Explanation:
 * - START: Built-in entry point (receives initial state)
 * - generate_script: Our custom node that calls OpenAI
 * - END: Built-in exit point (returns final state)
 *
 * The compiled workflow can be invoked with initial state and will:
 * 1. Start with the provided state
 * 2. Execute each node in sequence
 * 3. Merge state updates from each node
 * 4. Return the final state
 */
export function createScriptAgent() {
  console.log('🏗️ Creating Enhanced Script Generation Agent...')

  // Create a new StateGraph with our state schema
  const workflow = new StateGraph(ScriptState)
    // Add all our nodes
    .addNode('input_detection', inputDetectionNode)
    .addNode('blog_parsing', blogParsingNode)
    .addNode('pdf_parsing', pdfParsingNode)
    .addNode('text_parsing', textParsingNode)
    .addNode('generate_script', generateScriptNode)

    // Define the flow: START → input_detection → conditional routing
    .addEdge(START, 'input_detection')

    // Conditional routing based on input type
    .addConditionalEdges(
      'input_detection',
      // Router function: decides which node to go to next
      (state: ScriptStateType) => {
        console.log('🔀 Routing decision - Input type:', state.inputType)
        switch (state.inputType) {
          case 'blog_url':
            return 'blog_parsing'
          case 'pdf_url':
            return 'pdf_parsing'
          case 'text':
            return 'text_parsing'
          case 'idea':
          default:
            return 'generate_script'
        }
      },
      // Mapping of router outputs to node names
      {
        blog_parsing: 'blog_parsing',
        pdf_parsing: 'pdf_parsing',
        text_parsing: 'text_parsing',
        generate_script: 'generate_script',
      }
    )

    // Connect all parsing nodes to script generation
    .addEdge('blog_parsing', 'generate_script')
    .addEdge('pdf_parsing', 'generate_script')
    .addEdge('text_parsing', 'generate_script')
    .addEdge('generate_script', END)

  // Compile the graph into an executable workflow
  const compiledWorkflow = workflow.compile()
  console.log('✅ Enhanced Script Agent compiled successfully')
  console.log('🎯 Workflow supports both text ideas and blog URLs')

  return compiledWorkflow
}

/**
 * MAIN ENTRY POINT - High-Level Agent Interface
 *
 * This function provides a simple, clean interface for using the script agent.
 * It handles:
 * 1. Creating the agent workflow
 * 2. Setting up initial state
 * 3. Invoking the workflow
 * 4. Processing the results
 * 5. Error handling
 *
 * This is what external code calls to use the agent - it abstracts away
 * all the LangGraph complexity and provides a simple function interface.
 *
 * @param params - Object containing all script generation parameters
 * @returns Promise<Script> - The generated script object
 */
export async function generateVideoScript(params: {
  userIdea: string
  duration?: number
  tone?: string
  audience?: string
  platform?: string
  language?: string
  keywords?: string | null
  hook?: boolean
  callToAction?: boolean
}) {
  // Extract parameters with defaults
  const {
    userIdea,
    duration = 30,
    tone = 'friendly',
    audience = 'general audience',
    platform = 'YouTube',
    language = 'English',
    keywords = null,
    hook = false,
    callToAction = false,
  } = params

  console.log('🚀 Starting Script Generation Process')
  console.log('💡 User Idea:', userIdea)
  console.log('⏱️ Duration:', duration, 'seconds')
  console.log('🎭 Tone:', tone)
  console.log('👥 Audience:', audience)
  console.log('📱 Platform:', platform)
  console.log('🌍 Language:', language)
  console.log('🔑 Keywords:', keywords || 'None')
  console.log('🪝 Hook:', hook ? 'Yes' : 'No')
  console.log('📢 Call-to-Action:', callToAction ? 'Yes' : 'No')

  // Create the agent
  const agent = createScriptAgent()

  // Run the agent with initial state including all parameters
  const initialState = {
    userIdea,
    duration,
    tone,
    audience,
    platform,
    language,
    keywords,
    hook,
    callToAction,
    // New fields for content parsing
    inputType: 'idea' as const, // Will be detected by input_detection node
    blogContent: null,
    blogSummary: null,
    pdfContent: null,
    pdfSummary: null,
    textContent: null,
    textSummary: null,
    extractedKeywords: null,
    status: 'processing' as const,
    error: null,
  }

  console.log('🔄 Invoking script generation agent...')
  const result = await agent.invoke(initialState)

  console.log('📋 Final Result Status:', result.status)

  if (result.status === 'failed') {
    console.error('💥 Script generation failed:', result.error)
    throw new Error(result.error || 'Script generation failed')
  }

  if (result.status === 'completed' && result.script) {
    console.log('🎊 Script generation successful!')
    console.log('📝 Final Script:', JSON.stringify(result.script, null, 2))
    return result.script
  }

  throw new Error('Script generation completed but no script was generated')
}
